# SimpleQA Eval

SimpleQA: Measuring short-form factuality in large language models
Authors: <AUTHORS>
https://cdn.openai.com/papers/simpleqa.pdf


Run instructions: 

```python
python experiments/eval/run.py --current-dir . --dataset SimpleQA --split main --run-id 0 --simulated-user-type none --parallel 1 --config experiments/endpoint_configs/config_template.yaml --mode run --system-type LLM
```

```
@article{wei2024measuring,
  title={Measuring short-form factuality in large language models},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={arXiv preprint arXiv:2411.04368},
  year={2024}
}
```