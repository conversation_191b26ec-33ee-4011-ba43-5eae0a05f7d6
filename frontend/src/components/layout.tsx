import * as React from "react";
import { appContext } from "../hooks/provider";
import { useConfigStore } from "../hooks/store";
import "antd/dist/reset.css";
import { ConfigProvider, theme } from "antd";
import { SessionManager } from "./views/manager";
import { useTranslation } from 'react-i18next';
import LanguageSwitcher from './LanguageSwitcher';
import '../i18n';

const classNames = (...classes: (string | undefined | boolean)[]) => {
  return classes.filter(Boolean).join(" ");
};

type Props = {
  title: string;
  link: string;
  children?: React.ReactNode;
  showHeader?: boolean;
  restricted?: boolean;
  meta?: any;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
};

const TaiHeManusLayout = ({
  meta,
  title,
  link,
  showHeader = true,
  restricted = false,
  activeTab,
  onTabChange,
}: Props) => {
  const { darkMode, user, setUser } = React.useContext(appContext);
  const { sidebar } = useConfigStore();
  const { isExpanded } = sidebar;
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false);
  const { t } = useTranslation();

  // Mimic sign-in: if no user or user.email, set default user and localStorage
  React.useEffect(() => {
    if (!user?.email) {
      const defaultEmail = "default";
      setUser({ ...user, email: defaultEmail, name: defaultEmail });
      if (typeof window !== "undefined") {
        window.localStorage.setItem("user_email", defaultEmail);
      }
    }
  }, [user, setUser]);

  // Close mobile menu on route change
  React.useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [link]);

  React.useEffect(() => {
    document.getElementsByTagName("html")[0].className = `${
      darkMode === "dark" ? "dark bg-primary" : "light bg-primary"
    }`;
  }, [darkMode]);

  const layoutContent = (
    <div className="h-screen flex flex-col bg-primary">
      {/* Top Navigation Bar */}
      <div className="bg-secondary border-b border-primary px-4 py-3 flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-xl font-bold text-accent mr-4">
            {t('app.title')}
          </h1>
          <span className="text-sm text-secondary hidden md:inline">
            {t('app.subtitle')}
          </span>
        </div>
        <div className="flex items-center space-x-4">
          <LanguageSwitcher />
        </div>
      </div>

      {/* Content area */}
      <div className="flex-1 flex">
        <div
          className={classNames(
            "flex-1 flex flex-col",
            "transition-all duration-300 ease-in-out"
          )}
        >
          <ConfigProvider
            theme={{
              token: {
                borderRadius: 8,
                colorBgBase: darkMode === "dark" ? "#1e293b" : "#ffffff",
                colorPrimary: "#0891b2",
              },
              algorithm:
                darkMode === "dark"
                  ? theme.darkAlgorithm
                  : theme.defaultAlgorithm,
            }}
          >
            <main className="flex-1 p-4 text-primary bg-primary" style={{ height: "100%" }}>
              <SessionManager />
            </main>
          </ConfigProvider>
          <div className="text-xs text-secondary px-4 py-2 text-center bg-secondary border-t border-primary">
            {t('app.disclaimer')}
          </div>
        </div>
      </div>
    </div>
  );

  if (restricted) {
    return (
      <appContext.Consumer>
        {(context: any) => {
          if (context.user) {
            return layoutContent;
          }
          return null;
        }}
      </appContext.Consumer>
    );
  }

  return layoutContent;
};

export default TaiHeManusLayout;
