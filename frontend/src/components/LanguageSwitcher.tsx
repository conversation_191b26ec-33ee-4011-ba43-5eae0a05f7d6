import React from 'react';
import { useTranslation } from 'react-i18next';
import { Select } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';

const { Option } = Select;

interface LanguageSwitcherProps {
  className?: string;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ className = '' }) => {
  const { i18n } = useTranslation();

  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
  };

  const languages = [
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' }
  ];

  return (
    <div className={`flex items-center ${className}`}>
      <GlobalOutlined className="mr-2 text-primary" />
      <Select
        value={i18n.language}
        onChange={handleLanguageChange}
        className="min-w-[120px]"
        size="small"
        bordered={false}
      >
        {languages.map((lang) => (
          <Option key={lang.code} value={lang.code}>
            <span className="flex items-center">
              <span className="mr-2">{lang.flag}</span>
              {lang.name}
            </span>
          </Option>
        ))}
      </Select>
    </div>
  );
};

export default LanguageSwitcher;
