.dark {
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-light: #334155;
  --color-bg-tertiary: #475569;
  --color-bg-opposite: #f8fafc;
  --color-bg-white: #ffffff;
  --color-bg-dark: #020617;
  --color-bg-accent: #0891b2;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-accent: #06b6d4;
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
  --color-border-accent: #0891b2;
  --color-warning-primary: #f59e0b;
  --color-text-message: #f8fafc;

  --color-magenta-400: #f0abfc;
  --color-magenta-700: #c084fc;
  --color-magenta-800: #a855f7;
  --color-magenta-900: #9333ea;

  --color-blue-400: #60a5fa;
  --color-blue-700: #3b82f6;
  --color-blue-800: #2563eb;
  --color-blue-900: #1d4ed8;

  --color-gary-700: #64748b;
  --color-gray-800: #475569;
}

.light {
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-light: #f1f5f9;
  --color-bg-tertiary: #e2e8f0;
  --color-bg-opposite: #0f172a;
  --color-bg-white: #ffffff;
  --color-bg-dark: #1e293b;
  --color-bg-accent: #0891b2;
  --color-text-primary: #0f172a;
  --color-text-secondary: #64748b;
  --color-text-accent: #0891b2;
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-accent: #0891b2;
  --color-warning-primary: #f59e0b;
  --color-text-message: #0f172a;

  --color-magenta-400: #f0abfc;
  --color-magenta-700: #c084fc;
  --color-magenta-800: #a855f7;
  --color-magenta-900: #9333ea;

  --color-blue-400: #60a5fa;
  --color-blue-700: #3b82f6;
  --color-blue-800: #2563eb;
  --color-blue-900: #1d4ed8;

  --color-gary-700: #94a3b8;
  --color-gray-800: #64748b;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* @layer base {
    body {
      @apply dark:bg-slate-800 dark:text-white;
    }
  } */

/* Import Open Sans font */
@font-face {
  font-family: "Open Sans";
  src: url("./Open_Sans/OpenSans-VariableFont_wdth\,wght.ttf")
    format("truetype-variations");
  font-weight: 300 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Open Sans";
  src: url("./Open_Sans/OpenSans-Italic-VariableFont_wdth\,wght.ttf")
    format("truetype-variations");
  font-weight: 300 800;
  font-style: italic;
  font-display: swap;
}

html {
  /* display: table; */
  margin: auto;
  height: 100%;
  font-family: "Segoe UI", "Open Sans", sans-serif;;
}

body {
  padding: 0px 64px 0px 64px;
  @apply px-4 md:px-8 lg:px-16 w-full !important;
  margin: 0px auto;
  background: transparent;
  /* border: 2px solid green; */
  width: 100%;
  height: 100%;
  font-size: 0.9rem; /* Ensure consistent font size */
  font-family: "Segoe UI", "Open Sans", sans-serif;;
}

* {
  font-size: 0.9rem;
}

/* @import "antd/dist/antd.css"; */

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  @apply text-accent !important;
  font-weight: 500;
}
.ant-tabs-nav::before {
  @apply border-secondary !important;
}
.ant-tabs-tab:hover {
  @apply text-accent !important;
}

.ant-tabs-tab {
  @apply text-primary !important;
}

.ant-tabs-ink-bar {
  @apply bg-accent !important;
}

.element.style {
  left: 0%;
  width: 100%;
}
.ant-slider-track {
  @apply bg-accent !important;
}
.ant-slider-rail {
  @apply bg-secondary !important;
}
.ant-slider-handle {
  @apply border-accent !important;
}
.ant-slider-handle:hover {
  @apply border-accent brightness-75 !important;
}

.ant-switch-checked {
  background: var(--color-magenta-900) !important;
}
.ant-switch {
  background-color: #3a3a3a;
  border: grey;
}

/* .ant-modal-content {
  @apply dark:bg-primary dark:text-primary;
}
.ant-modal-footer {
  @apply border-secondary;
}
.ant-modal-header,
.ant-modal-close {
  @apply bg-secondary text-primary hover:text-primary    transition duration-200;
}
.ant-modal-title,
.ant-modal-header {
  @apply bg-primary text-primary;
} */
a:hover {
  @apply text-accent;
}
/* .iiz__img,
  iiz__zoom-img {
    @apply w-full;
  } */

.ant-radio-checked .ant-radio-inner {
  @apply border-accent !important;
}

.ant-radio-checked .ant-radio-inner:after {
  @apply bg-accent !important;
}

.ant-radio:hover .ant-radio-inner {
  @apply border-accent !important;
}

.loadbar:after {
  content: "";
  /* width: 40px; */
  height: 3px;
  /* background: red; */

  position: absolute;
  animation: loader 2s;
  -webkit-animation: loader 2s;
  animation-iteration-count: infinite;
  -webkit-animation-iteration-count: infinite;
  transition-timing-function: linear;
  -webkit-transition-timing-function: linear;
  bottom: 0px;
  @apply rounded-b bg-accent;

  margin-left: 0;
}

@keyframes loader {
  0% {
    width: 0%;
    left: 0;
    right: 0;
  }
  50% {
    width: 100%;
    left: 0;
    right: 0;
  }
  99% {
    width: 0%;
    left: 100%;
    right: 0;
  }
}

@-webkit-keyframes loader {
  0% {
    width: 0%;
    left: 0;
  }
  50% {
    width: 100%;
    left: 0;
    right: 0;
  }
  99% {
    width: 0%;
    left: 100%;
    right: 0;
  }
}
.scroll::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  opacity: 0; /* Make scrollbar fully transparent by default */
  transition: opacity 0.25s ease; /* Transition for the opacity */
}

.scroll:hover::-webkit-scrollbar {
  opacity: 1; /* Make scrollbar fully opaque on hover */
}

.scroll::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 20px;
}

.scroll::-webkit-scrollbar-thumb {
  @apply bg-accent;
  border-radius: 20px;
  border: 3px solid rgb(214, 214, 214);
}

.dark .scroll-gradient {
}

.light .scroll-gradient {
}

.scroll-gradient {
  position: sticky;
  top: 0;
  height: 15px;
  z-index: 2; /* Above the content but below the scrollbar */
  pointer-events: none; /* So it doesn't block interaction with the content */
}

.vega-embed {
  @apply bg-primary rounded !important;
}

.ant-upload-list,
.ant-upload-hint,
.ant-upload-list-text,
.ant-upload-text,
.ant-upload-text-icon {
  @apply text-primary !important;
}

.ant-upload {
  @apply text-primary px-2  !important;
  border-radius: 4px !important;
}
.ant-upload:hover {
  @apply border-accent !important;
}
.ant-upload-drag-container,
.ant-upload-text,
.ant-btn,
.ant-select,
.ant-select > span,
.ant-select-selector,
.ant-form-item-required,
.ant-upload-hint {
  @apply text-primary !important;
}

.ant-upload-list-item:hover,
.ant-upload-list-item-info:hover {
  @apply bg-secondary text-accent !important;
}
.ant-pagination .ant-pagination-item,
.ant-select-selector,
.ant-btn-variant-outlined {
  @apply bg-primary  !important;
}
.ant-pagination .ant-pagination-item-active a {
  @apply text-accent  !important;
}
.ant-pagination .ant-pagination-item-active {
  @apply border-accent text-accent !important;
}
.ant-pagination .ant-pagination-item a,
.ant-pagination-item-link .anticon {
  @apply text-primary !important;
}
.ant-collapse-expand-icon .anticon {
  @apply text-primary !important;
}
.ant-modal-content {
  @apply dark:bg-primary dark:text-primary !important;
}
.ant-modal-footer {
  @apply border-secondary !important;
}
.ant-btn,
.ant-select-arrow,
.ant-btn:hover {
  @apply text-primary !important;
}

:where(.ant-btn).ant-btn-compact-item.ant-btn-primary:not([disabled])
  + .ant-btn-compact-item.ant-btn-primary:not([disabled]):before {
  @apply bg-secondary !important;
}
.ant-btn-primary {
  @apply bg-accent text-white !important;
}
.ant-btn-primary:hover {
  @apply bg-accent text-white drop-shadow-md !important;
}
.ant-modal-close {
  @apply text-primary   duration-200   !important;
}

.ant-radio,
.ant-collapse,
.ant-collapse-header-text,
.ant-collapse-content-box,
.ant-collapse-content,
.ant-radio-wrapper {
  @apply text-primary !important;
}
.ant-collapse-borderless > .ant-collapse-item {
  @apply border-secondary !important;
}

.ant-skeleton-paragraph > li {
  @apply bg-secondary !important;
}

.ant-drawer-content,
.ant-drawer-header,
.ant-drawer-header-title,
.ant-drawer-close,
.ant-drawer-title {
  @apply bg-primary text-primary !important;
}

.ant-dropdown-menu {
  max-height: 250px;
  overflow: auto;
  @apply scroll !important;
}

.ant-collapse {
  @apply bg-secondary border-primary !important;
}

.ant-collapse-header {
  @apply bg-primary text-primary border-primary !important;
  align-items: center !important;
}

.ant-collapse-content {
  @apply bg-primary border-primary !important;
}

.ant-collapse-item {
  @apply border-primary !important;
}

/* .ant-radio-input::before {
    @apply bg-primary !important;
  } */

.prose > pre {
  padding: 0px !important;
  margin: 0px !important;
}

.prose p {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.monaco-editor,
.monaco-scrollable-element,
.overflow-guard {
  @apply rounded !important;
}

/* div.chatbox > ul {
    list-style: disc !important;
  }
  div.chatbox > ul,
  div.chatbox > ol {
    padding-left: 20px !important;
    margin: 0px !important;
  }
  div.chatbox > ol {
    list-style: decimal !important;
  } */

div#___gatsby,
div#gatsby-focus-wrapper {
  height: 100%;
  /* border: 1px solid green; */
}

.ant-input {
  @apply bg-primary !important;
}