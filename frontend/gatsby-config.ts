import type { GatsbyConfig } from "gatsby";
import fs from "fs";

const envFile = `.env.${process.env.NODE_ENV}`;

fs.access(envFile, fs.constants.F_OK, (err) => {
  if (err) {
    console.warn(`File '${envFile}' is missing. Using default values.`);
  }
});

require("dotenv").config({
  path: envFile,
});

const config: GatsbyConfig = {
  pathPrefix: process.env.PREFIX_PATH_VALUE || "",
  siteMetadata: {
    title: `TaiHeManus`,
    description: `智能多代理协作平台 - Intelligent Multi-Agent Collaboration Platform`,
    siteUrl: `http://tbd.place`,
  },
  // More easily incorporate content into your pages through automatic TypeScript type generation and better GraphQL IntelliSense.
  // If you use VSCode you can also use the GraphQL plugin
  // Learn more at: https://gatsby.dev/graphql-typegen
  graphqlTypegen: true,
  plugins: [
    "gatsby-plugin-postcss",
    // "gatsby-plugin-image", // Temporarily disabled due to sharp issues
    "gatsby-plugin-sitemap",
    // {
    //   resolve: "gatsby-plugin-manifest",
    //   options: {
    //     // icon: "src/images/icon.png", // Temporarily disabled due to sharp issues
    //   },
    // },
    "gatsby-plugin-mdx",
    // "gatsby-plugin-sharp", // Temporarily disabled due to sharp issues
    // "gatsby-transformer-sharp", // Temporarily disabled due to sharp issues
    {
      resolve: "gatsby-source-filesystem",
      options: {
        name: "images",
        path: "./src/images/",
      },
      __key: "images",
    },
    {
      resolve: "gatsby-source-filesystem",
      options: {
        name: "pages",
        path: "./src/pages/",
      },
      __key: "pages",
    },
  ],
};

export default config;
